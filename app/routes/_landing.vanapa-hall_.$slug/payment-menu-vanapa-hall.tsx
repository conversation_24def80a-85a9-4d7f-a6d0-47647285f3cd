import { useState, type ChangeEvent } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { NavLink } from "react-router"

import { IconLoader, IconX } from "@components/icons"
import { ImageButton } from "@components/ui"
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Button } from "@/components/ui/button"
import usePaymentCalculation from "@/lib/hooks/use-payment-calculation"
import { Tabs, TabsContent } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { BackButton, Rupee } from "@/components/common"
import razorpayLoaderStore from "@/lib/store/razorpay-loader-store"
import useCreateSportEventPayment from "@/routes/_landing.msl_.$slug/create-sport-event-payment"
import useGetSeats from "@/routes/_landing.msl_.$slug/use-get-seats"
import VanapaHall from "@/components/stadiums/vanapa-hall"
import type { FormType } from "./schema"
import { schema } from "./schema"

interface Props {
  description?: string
}

const PaymentMenuVanapaHall = ({ description }: Props) => {
  const [open, setOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("seating")

  const { setLoading } = razorpayLoaderStore()

  const openModal = () => {
    setOpen(true)
  }

  const { pricing, calculatePricing } = usePaymentCalculation()
  const { getSeats } = useGetSeats()
  const { createPayment } = useCreateSportEventPayment()

  const form = useForm<FormType>({
    resolver: zodResolver(schema),
    defaultValues: {
      username: "",
      mobileNumber: "",
      tipAmount: "" as unknown as number,
      tipMessage: "",
      seatNumbers: [],
      numberOfTickets: 0,
      ticketTypeId: "" as unknown as number,
    },
  })

  // used for checking disabled status of continue
  const seatingNumbers = form.watch("seatNumbers")
  const numberOfTickets = form.watch("numberOfTickets")

  const handleContinue = () => {
    if (numberOfTickets && numberOfTickets > 0) {
      // for general tickets
      // calculatePricing({
      //   convenienceAmount: selectedArea.convenienceFee,
      //   selectedNumberOfTickets: numberOfTickets,
      //   ticketPrice: selectedArea.price,
      // })
      setActiveTab("purchase")
    } else if (seatingNumbers.length > 0) {
      if (seatingNumbers.length > 5) {
        form.setError("seatNumbers", {
          message: "Only 5 seats allowed at a time",
        })
      } else {
        // for non general tickets
        // calculatePricing({
        //   convenienceAmount: selectedArea.convenienceFee,
        //   selectedNumberOfTickets: seatingNumbers.length,
        //   ticketPrice: selectedArea.price,
        // })
        setActiveTab("purchase")
      }
    }
  }

  const handleSeatSelect = ({
    e,
    seat,
  }: {
    e: ChangeEvent<HTMLInputElement>
    seat: string
  }) => {
    const currentValues = form.getValues("seatNumbers")
    if (e.target.checked) {
      form.setValue("seatNumbers", [...currentValues, seat])
    } else {
      form.setValue(
        "seatNumbers",
        currentValues.filter((val) => val !== seat)
      )
    }
  }

  const handleAddTip = () => {
    const tipAmount = form.getValues("tipAmount")

    if (tipAmount) {
      const parsedAmount = parseInt(tipAmount.toString(), 10)
      if (numberOfTickets && numberOfTickets > 0) {
        // calculatePricing({
        //   convenienceAmount: selectedArea.convenienceFee,
        //   selectedNumberOfTickets: numberOfTickets,
        //   ticketPrice: pricing.ticketPrice,
        //   tipAmount: parsedAmount,
        // })
      } else if (seatingNumbers.length > 0) {
        return
      }
      // calculatePricing({
      //   convenienceAmount: selectedArea.convenienceFee,
      //   selectedNumberOfTickets: seatingNumbers.length,
      //   ticketPrice: pricing.ticketPrice,
      //   tipAmount: parsedAmount,
      // })
    }
  }

  const handleCancelTip = () => {
    if (numberOfTickets && numberOfTickets > 0) {
      // calculatePricing({
      //   convenienceAmount: selectedArea.convenienceFee,
      //   selectedNumberOfTickets: numberOfTickets,
      //   ticketPrice: pricing.ticketPrice,
      //   tipAmount: 0,
      // })
    } else if (seatingNumbers.length > 0)
      // calculatePricing({
      //   convenienceAmount: selectedArea.convenienceFee,
      //   selectedNumberOfTickets: seatingNumbers.length,
      //   ticketPrice: pricing.ticketPrice,
      //   tipAmount: 0,
      // })

      form.setValue("tipAmount", undefined)
    form.setValue("tipMessage", undefined)
  }

  const handleCreatePayment = async (data: FormType) => {
    // createPayment.mutate(data, {
    //   onSuccess: (data) => {
    //     const providerType = data?.createSportEventPayment?.provider_type
    //     switch (providerType) {
    //       case "razorpay":
    //         setOpen(false)
    //         handleRazorpay({
    //           amount: pricing.totalAmountPayable,
    //           username: data.createSportEventPayment.username,
    //           mobileNumber: data.createSportEventPayment.mobile_number,
    //           orderId: data.createSportEventPayment.order_id,
    //           handlerCallback: () => {
    //             form.reset()
    //           },
    //           setLoading: setLoading,
    //         })
    //         break
    //       case "phonepe":
    //         handlePhonePe(data.createSportEventPayment.goto_url ?? "")
    //         break
    //       case "sgpg":
    //         handleSGPG(data.createSportEventPayment.goto_url ?? "")
    //     }
    //   },
    //   onError: (error) => {
    //     toast.error(parseGraphqlError(error))
    //   },
    // })
  }

  // reset data when menu is toggled
  const handleClose = (open: boolean) => {
    if (!open) {
      form.reset()
      setActiveTab("seating")
    }
    setOpen(open)
  }

  return (
    <>
      <div className="flex w-full justify-center border-primary-blue">
        <ImageButton
          onClick={openModal}
          type="button"
          loaderVariant="smallWhite"
          imageSrc="/buy-btn.png"
        >
          Buy Ticket
        </ImageButton>
      </div>
      <Sheet open={open} onOpenChange={handleClose}>
        <SheetContent
          side="bottom"
          className="mx-auto mb-1 h-full max-w-lg overflow-y-scroll px-0 py-8 scrollbar-thin scrollbar-track-slate-300 scrollbar-thumb-slate-700 scrollbar-track-rounded-full scrollbar-thumb-rounded-full"
        >
          <SheetHeader>
            <img
              src="/logo.png"
              alt="logo"
              className="mx-auto mt-4 flex w-16 justify-center"
            />
            <SheetTitle className="sr-only">Payment menu</SheetTitle>
            <SheetDescription className="sr-only">
              Buy tickets for {description}
            </SheetDescription>
          </SheetHeader>
          <SheetClose
            className="absolute right-4 top-4"
            onClick={() => setOpen(false)}
          >
            <IconX />
          </SheetClose>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleCreatePayment)}
              className="flex flex-col gap-y-4 px-4 pt-8 text-sm"
            >
              <Tabs value={activeTab}>
                <TabsContent value="purchase" className="flex flex-col gap-y-4">
                  <div>
                    <BackButton onClick={() => setActiveTab("seating")} />
                  </div>
                  <div className="space-y-4 rounded-lg bg-gray-200 p-4">
                    <div className="flex justify-between">
                      <div>Ticket</div>
                      <div>Quantity</div>
                    </div>
                    <div className="flex justify-between font-bold">
                      {/* <div>{formatKebab(createSeats.name)}</div> */}
                      {/* <div> */}
                      {/*   {createSeats.name.includes("general") */}
                      {/*     ? `x ${numberOfTickets}` */}
                      {/*     : `x ${seatingNumbers.length}`} */}
                      {/* </div> */}
                    </div>
                  </div>
                  <FormField
                    control={form.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel required>Name</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Name" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="mobileNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel required>WhatsApp number</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            inputMode="tel"
                            className="number-input"
                            maxLength={10}
                            placeholder={"WhatsApp number"}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {/* <DonationDialog */}
                  {/*   callback={handleAddTip} */}
                  {/*   cancelCallback={handleCancelTip} */}
                  {/*   form={form} */}
                  {/* /> */}
                  <div className="flex flex-col">
                    <p className="flex justify-between">
                      <span>Total amount: </span>
                      <span>
                        {isNaN(pricing.totalAmount) ? (
                          <>
                            <Rupee /> 0.00
                          </>
                        ) : (
                          <>
                            <Rupee /> {pricing.totalAmount.toFixed(2)}
                          </>
                        )}
                      </span>
                    </p>
                    {pricing.discountAmount > 0 && (
                      <p className="flex justify-between text-gray-400">
                        <span className="text-green-700">Discount:</span>
                        <span>
                          - <Rupee /> {pricing.discountAmount.toFixed(2)}
                        </span>
                      </p>
                    )}
                    {pricing.gstAmount > 0 && (
                      <p className="flex justify-between text-gray-400">
                        <span>GST 18%:</span>
                        <span>{pricing.gstAmount.toFixed(2)}</span>
                      </p>
                    )}
                    {pricing.convenienceFee > 0 && (
                      <p className="flex justify-between text-gray-400">
                        <span>Processing fee: </span>
                        <span>
                          {isNaN(pricing.convenienceFee) ? (
                            <>
                              <Rupee /> 0.00
                            </>
                          ) : (
                            <>
                              <Rupee /> {pricing.convenienceFee.toFixed(2)}
                            </>
                          )}
                        </span>
                      </p>
                    )}
                    {pricing.tipAmount > 0 && (
                      <p className="flex justify-between text-pallet-green">
                        <span>Tip:</span>
                        <span>
                          <Rupee /> {pricing.tipAmount.toFixed(2)}
                        </span>
                      </p>
                    )}
                    <p className="mt-2 flex justify-between border-t border-gray-700 pt-2">
                      <span>Total amount payable: </span>
                      <span>
                        {isNaN(pricing.totalAmountPayable) ? (
                          <>
                            <Rupee /> 0.00
                          </>
                        ) : (
                          <>
                            <Rupee /> {pricing.totalAmountPayable.toFixed(2)}
                          </>
                        )}
                      </span>
                    </p>
                  </div>

                  <div className="mt-4">
                    Ticket once bought cannot be refunded. In case of payment
                    issues contact our customer care. For more information see{" "}
                    <NavLink
                      target="_blank"
                      to="/terms-and-conditions"
                      className="underline"
                    >
                      Terms & Conditions
                    </NavLink>
                    <FormField
                      control={form.control}
                      name="checkTerms"
                      render={({ field }) => (
                        <FormItem className="mt-4 flex flex-col">
                          <div className="flex space-x-2">
                            <FormLabel>
                              <span className="text-destructive">* </span>I
                              agree to the terms of use and privacy policy
                            </FormLabel>
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </div>
                          <FormMessage className="text-foreground" />
                        </FormItem>
                      )}
                    />
                  </div>
                  <Button
                    className="mt-6 w-full shadow-md"
                    variant="palletYellow"
                    type="submit"
                    isLoading={createPayment.isPending}
                  >
                    Buy Ticket
                  </Button>
                </TabsContent>

                <TabsContent value="seating" className="flex flex-col gap-y-4">
                  <div className="flex justify-center whitespace-pre">
                    {description}
                  </div>
                  <VanapaHall
                    seatNumbers={seatingNumbers}
                    handleSeatSelect={handleSeatSelect}
                  />

                  {getSeats.isPending && (
                    <>
                      <div className="mt-4 text-center">
                        Fetching seats. Please wait...
                      </div>
                      <IconLoader className="mx-auto animate-spin" />
                    </>
                  )}

                  {getSeats.isError && (
                    <>
                      <div className="my-4 text-center font-bold">
                        Error. Unable to fetch seats. Please contact the
                        organizer.
                      </div>
                    </>
                  )}

                  <Button
                    className="mt-6 w-full shadow-md"
                    variant="palletYellow"
                    type="button"
                    onClick={handleContinue}
                    disabled={
                      numberOfTickets === 0 && seatingNumbers.length === 0
                    }
                  >
                    Continue
                  </Button>
                </TabsContent>
              </Tabs>
            </form>
          </Form>
        </SheetContent>
      </Sheet>
    </>
  )
}

export default PaymentMenuVanapaHall
