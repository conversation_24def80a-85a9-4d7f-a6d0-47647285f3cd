import type { ChangeEvent } from "react"
import { CheckboxGroup, CheckboxGroupItem } from "../ui/checkbox"

interface Props {
  handleSeatSelect: ({
    e,
    seat,
  }: {
    e: ChangeEvent<HTMLInputElement>
    seat: string
  }) => void
  seatNumbers: string[]
  occupiedSeats: string[]
}

const VanapaHallFront = ({ handleSeatSelect, seatNumbers }: Props) => {
  return (
    <>
      {/* F152-175 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 152}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 164}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>

      {/* F128 - 151 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 128}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 140}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>

      {/* F106 - 127 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 105}`
            if (index < 1) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 117}`
            if (index > 10) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>

      {/* F84 - 105 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 83}`
            if (index < 1) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 95}`
            if (index > 10) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>

      {/* F62 - 83 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 61}`
            if (index < 1) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 73}`
            if (index > 10) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>

      {/* F41 - 82 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 39}`
            if (index < 2) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 51}`
            if (index > 10) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>

      {/* F21 - 40 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 19}`
            if (index < 2) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 31}`
            if (index > 9) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>

      {/* F1 - 20 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index - 1}`
            if (index < 2) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `F${index + 11}`
            if (index > 9) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>
    </>
  )
}

export default VanapaHallFront
