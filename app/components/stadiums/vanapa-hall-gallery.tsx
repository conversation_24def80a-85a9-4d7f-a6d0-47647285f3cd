import type { ChangeEvent } from "react"
import { CheckboxGroup, CheckboxGroupItem } from "../ui/checkbox"

interface Props {
  handleSeatSelect: ({
    e,
    seat,
  }: {
    e: ChangeEvent<HTMLInputElement>
    seat: string
  }) => void
  seatNumbers: string[]
  occupiedSeats: string[]
}

const VanapaHallGallery = ({
  handleSeatSelect,
  seatNumbers,
  occupiedSeats,
}: Props) => {
  return (
    <>
      {/* 133 - 148 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `G${index + 128}`
            if (index < 5) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                  disabled={occupiedSeats.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `G${index + 140}`
            if (index > 8) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                  disabled={occupiedSeats.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>

      {/* 115 - 132 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `G${index + 112}`
            if (index < 3) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                  disabled={occupiedSeats.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `G${index + 124}`
            if (index > 8) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                  disabled={occupiedSeats.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>

      {/* 95 - 114 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `G${index + 93}`
            if (index < 2) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                  disabled={occupiedSeats.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `G${index + 105}`
            if (index > 9) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                  disabled={occupiedSeats.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>

      {/* 73 - 94 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `G${index + 72}`
            if (index === 0) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                  disabled={occupiedSeats.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `G${index + 84}`
            if (index > 10) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                  disabled={occupiedSeats.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>

      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `G${index + 49}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `G${index + 61}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>

      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `G${index + 25}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `G${index + 37}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>

      <div className="col-span-5 ">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `G${index + 1}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `G${index + 13}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
    </>
  )
}

export default VanapaHallGallery
