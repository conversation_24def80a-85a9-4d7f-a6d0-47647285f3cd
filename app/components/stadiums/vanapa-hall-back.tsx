import type { ChangeEvent } from "react"
import { CheckboxGroup, CheckboxGroupItem } from "../ui/checkbox"

interface Props {
  handleSeatSelect: ({
    e,
    seat,
  }: {
    e: ChangeEvent<HTMLInputElement>
    seat: string
  }) => void
  seatNumbers: string[]
  occupiedSeats: string[]
}

const VanapaHallBack = ({
  handleSeatSelect,
  seatNumbers,
  occupiedSeats,
}: Props) => {
  return (
    <>
      {/* B187 - 206 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 185}`
            if (index < 2) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                  disabled={occupiedSeats.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 197}`
            if (index > 9) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                  disabled={occupiedSeats.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>

      {/* B167 - 186 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 165}`
            if (index < 2) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                  disabled={occupiedSeats.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 177}`
            if (index > 9) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                  disabled={occupiedSeats.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>

      {/* B145 - 166 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 144}`
            if (index < 1) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                  disabled={occupiedSeats.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 156}`
            if (index > 10) {
              return <div key={seatNumber} className="col-span-1" />
            } else
              return (
                <CheckboxGroupItem
                  key={seatNumber}
                  id={seatNumber}
                  variant="seat"
                  className="col-span-1 rounded-lg text-[0.5rem]"
                  value={seatNumber}
                  onChange={(e) => {
                    handleSeatSelect({ e, seat: seatNumber })
                  }}
                  checked={seatNumbers.includes(seatNumber)}
                  disabled={occupiedSeats.includes(seatNumber)}
                >
                  {seatNumber}
                </CheckboxGroupItem>
              )
          })}
        </CheckboxGroup>
      </div>

      {/* B121 - 144 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 121}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 133}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>

      {/* B97 - 120 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 97}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 109}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>

      {/* B73 - 96 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 73}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 85}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>

      {/* B49 - 72 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 49}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 61}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>

      {/* B25 - 48 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 25}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 37}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>

      {/* B1 - 24 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 1}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `B${index + 13}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
                disabled={occupiedSeats.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
    </>
  )
}

export default VanapaHallBack
