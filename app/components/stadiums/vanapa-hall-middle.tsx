import type { ChangeEvent } from "react"
import { CheckboxGroup, CheckboxGroupItem } from "../ui/checkbox"

interface Props {
  handleSeatSelect: ({
    e,
    seat,
  }: {
    e: ChangeEvent<HTMLInputElement>
    seat: string
  }) => void
  seatNumbers: string[]
}

const VanapaHallMiddle = ({ handleSeatSelect, seatNumbers }: Props) => {
  return (
    <>
      {/* M97 - 120 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `M${index + 97}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `M${index + 109}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>

      {/* M73 - 96 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `M${index + 73}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `M${index + 85}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>

      {/* M49 - 72 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `M${index + 49}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `M${index + 61}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>

      {/* M25 - 48 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `M${index + 25}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `M${index + 37}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>

      {/* M1 - 24 */}
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `M${index + 1}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
      <div className="col-span-1" />
      <div className="col-span-5">
        <CheckboxGroup className="grid grid-cols-12 gap-2">
          {Array.from({ length: 12 }, (_, index) => {
            const seatNumber = `M${index + 13}`
            return (
              <CheckboxGroupItem
                key={seatNumber}
                id={seatNumber}
                variant="seat"
                className="col-span-1 rounded-lg text-[0.5rem]"
                value={seatNumber}
                onChange={(e) => {
                  handleSeatSelect({ e, seat: seatNumber })
                }}
                checked={seatNumbers.includes(seatNumber)}
              >
                {seatNumber}
              </CheckboxGroupItem>
            )
          })}
        </CheckboxGroup>
      </div>
    </>
  )
}

export default VanapaHallMiddle
